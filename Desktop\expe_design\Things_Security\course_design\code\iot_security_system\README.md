# 物联网安全防护系统

基于课程设计论文实现的物联网安全防护系统，包含多层次安全防护机制。

## 项目结构

```
iot_security_system/
├── README.md                    # 项目说明
├── requirements.txt             # 依赖包
├── main.py                     # 主程序入口
├── config/                     # 配置文件
│   ├── __init__.py
│   ├── settings.py             # 系统配置
│   └── crypto_config.py        # 加密配置
├── core/                       # 核心模块
│   ├── __init__.py
│   ├── device_manager.py       # 设备管理
│   ├── crypto_engine.py        # 加密引擎
│   ├── threat_detector.py      # 威胁检测
│   ├── network_security.py     # 网络安全
│   ├── privacy_protection.py   # 隐私保护
│   ├── blockchain_manager.py   # 区块链管理
│   └── security_orchestrator.py # 安全编排
├── utils/                      # 工具模块
│   ├── __init__.py
│   ├── logger.py               # 日志工具
│   ├── database.py             # 数据库工具
│   └── network_utils.py        # 网络工具
├── models/                     # 数据模型
│   ├── __init__.py
│   ├── device_model.py         # 设备模型
│   ├── threat_model.py         # 威胁模型
│   └── security_event.py       # 安全事件模型
├── tests/                      # 测试模块
│   ├── __init__.py
│   ├── test_device_manager.py
│   ├── test_crypto_engine.py
│   └── test_threat_detector.py
└── data/                       # 数据文件
    ├── device_registry.json    # 设备注册表
    ├── threat_intelligence.json # 威胁情报
    └── security_logs/          # 安全日志
```

## 核心功能

1. **设备身份管理** - 基于区块链的设备认证和生命周期管理
2. **轻量级加密** - 实现LED-64、NTRU等轻量级加密算法
3. **威胁检测** - 基于机器学习的入侵检测系统
4. **网络安全** - SDN流量管控和协议安全检测
5. **隐私保护** - 联邦学习和同态加密实现
6. **安全编排** - 跨层协同防御机制

## 安装和运行

```bash
# 安装依赖
pip install -r requirements.txt

# 运行系统
python main.py
```

## 技术特点

- 模块化设计，易于扩展
- 支持多种加密算法
- 实时威胁检测
- 跨层协同防御
- 符合国密标准
