"""
系统配置文件
基于论文中的安全防护策略配置
"""

import os
from typing import Dict, Any

class SecurityConfig:
    """安全配置类"""
    
    # 系统基础配置
    SYSTEM_NAME = "IoT Security Protection System"
    VERSION = "1.0.0"
    DEBUG = True
    
    # 数据库配置
    DATABASE = {
        'type': 'sqlite',
        'path': 'data/security.db',
        'backup_interval': 3600  # 1小时备份一次
    }
    
    # Redis配置（用于缓存和消息队列）
    REDIS_CONFIG = {
        'host': 'localhost',
        'port': 6379,
        'db': 0,
        'password': None
    }
    
    # 日志配置
    LOGGING = {
        'level': 'INFO',
        'format': '{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}',
        'rotation': '100 MB',
        'retention': '30 days',
        'log_dir': 'data/security_logs'
    }
    
    # 设备管理配置
    DEVICE_CONFIG = {
        'max_devices': 10000,
        'heartbeat_interval': 30,  # 心跳间隔（秒）
        'offline_threshold': 120,  # 离线阈值（秒）
        'certificate_validity': 86400 * 30,  # 证书有效期30天
        'auto_registration': False
    }
    
    # 威胁检测配置
    THREAT_DETECTION = {
        'detection_interval': 5,  # 检测间隔（秒）
        'anomaly_threshold': 0.8,  # 异常阈值
        'ml_model_update_interval': 3600,  # 模型更新间隔（秒）
        'threat_intelligence_update': 1800,  # 威胁情报更新间隔（秒）
        'max_attack_attempts': 5,  # 最大攻击尝试次数
        'blacklist_duration': 3600  # 黑名单持续时间（秒）
    }
    
    # 网络安全配置
    NETWORK_SECURITY = {
        'sdn_controller_host': 'localhost',
        'sdn_controller_port': 6653,
        'flow_table_timeout': 300,  # 流表超时时间（秒）
        'ddos_threshold': 1000,  # DDoS攻击阈值（请求/秒）
        'rate_limit': 100,  # 速率限制（请求/秒）
        'protocol_whitelist': ['MQTT', 'CoAP', 'HTTP', 'HTTPS']
    }
    
    # 区块链配置
    BLOCKCHAIN_CONFIG = {
        'network_type': 'consortium',  # 联盟链
        'consensus_algorithm': 'PBFT',
        'block_time': 5,  # 出块时间（秒）
        'gas_limit': 8000000,
        'node_count': 4,
        'smart_contract_address': None
    }
    
    # 联邦学习配置
    FEDERATED_LEARNING = {
        'rounds': 100,
        'local_epochs': 5,
        'batch_size': 32,
        'learning_rate': 0.01,
        'min_clients': 3,
        'client_fraction': 0.8,
        'privacy_budget': 1.0  # 差分隐私预算
    }
    
    # API配置
    API_CONFIG = {
        'host': '0.0.0.0',
        'port': 8080,
        'cors_enabled': True,
        'rate_limit': '100/minute',
        'jwt_secret': 'your-secret-key-here',
        'jwt_expiration': 3600
    }
    
    # 监控配置
    MONITORING = {
        'metrics_port': 9090,
        'health_check_interval': 30,
        'alert_thresholds': {
            'cpu_usage': 80,
            'memory_usage': 85,
            'disk_usage': 90,
            'network_latency': 1000  # ms
        }
    }

class CryptoConfig:
    """加密配置类"""
    
    # 国密算法配置
    SM_ALGORITHMS = {
        'SM2': {
            'curve': 'sm2p256v1',
            'key_size': 256
        },
        'SM3': {
            'digest_size': 32
        },
        'SM4': {
            'key_size': 128,
            'block_size': 128
        }
    }
    
    # 轻量级加密算法配置
    LIGHTWEIGHT_CRYPTO = {
        'LED': {
            'key_size': 64,
            'block_size': 64,
            'rounds': 32
        },
        'PRESENT': {
            'key_size': 80,
            'block_size': 64,
            'rounds': 31
        },
        'SIMON': {
            'key_size': 128,
            'block_size': 128,
            'rounds': 68
        }
    }
    
    # TLS配置
    TLS_CONFIG = {
        'version': 'TLSv1.3',
        'cipher_suites': [
            'TLS_AES_256_GCM_SHA384',
            'TLS_CHACHA20_POLY1305_SHA256',
            'TLS_AES_128_GCM_SHA256'
        ],
        'certificate_path': 'certs/',
        'private_key_path': 'keys/',
        'ca_cert_path': 'ca/'
    }
    
    # 密钥管理配置
    KEY_MANAGEMENT = {
        'key_rotation_interval': 86400 * 7,  # 7天轮换一次
        'key_backup_enabled': True,
        'hsm_enabled': False,  # 硬件安全模块
        'key_derivation_function': 'PBKDF2',
        'salt_length': 32
    }

def get_config() -> Dict[str, Any]:
    """获取完整配置"""
    return {
        'security': SecurityConfig.__dict__,
        'crypto': CryptoConfig.__dict__
    }

def load_env_config():
    """从环境变量加载配置"""
    from dotenv import load_dotenv
    load_dotenv()
    
    # 从环境变量覆盖配置
    if os.getenv('DEBUG'):
        SecurityConfig.DEBUG = os.getenv('DEBUG').lower() == 'true'
    
    if os.getenv('DATABASE_PATH'):
        SecurityConfig.DATABASE['path'] = os.getenv('DATABASE_PATH')
    
    if os.getenv('REDIS_HOST'):
        SecurityConfig.REDIS_CONFIG['host'] = os.getenv('REDIS_HOST')
    
    if os.getenv('API_PORT'):
        SecurityConfig.API_CONFIG['port'] = int(os.getenv('API_PORT'))

# 初始化配置
load_env_config()
