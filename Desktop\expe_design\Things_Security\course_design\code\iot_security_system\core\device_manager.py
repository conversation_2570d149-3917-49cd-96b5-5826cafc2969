"""
设备管理模块
实现论文中提到的设备身份认证和生命周期管理
"""

import json
import time
import hashlib
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography import x509
from cryptography.x509.oid import NameOID

from config.settings import SecurityConfig
from utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class DeviceInfo:
    """设备信息数据类"""
    device_id: str
    device_type: str
    manufacturer: str
    model: str
    firmware_version: str
    mac_address: str
    ip_address: str
    public_key: str
    certificate: str
    registration_time: float
    last_heartbeat: float
    status: str  # online, offline, suspicious, blocked
    security_level: int  # 1-5, 5为最高安全级别
    
class DeviceManager:
    """设备管理器类"""
    
    def __init__(self):
        self.devices: Dict[str, DeviceInfo] = {}
        self.device_registry_file = "data/device_registry.json"
        self.certificate_authority = self._init_ca()
        self.load_device_registry()
        
    def _init_ca(self) -> Tuple[rsa.RSAPrivateKey, x509.Certificate]:
        """初始化证书颁发机构"""
        try:
            # 生成CA私钥
            ca_private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048
            )
            
            # 创建CA证书
            subject = issuer = x509.Name([
                x509.NameAttribute(NameOID.COUNTRY_NAME, "CN"),
                x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Beijing"),
                x509.NameAttribute(NameOID.LOCALITY_NAME, "Beijing"),
                x509.NameAttribute(NameOID.ORGANIZATION_NAME, "IoT Security CA"),
                x509.NameAttribute(NameOID.COMMON_NAME, "IoT Security Root CA"),
            ])
            
            ca_cert = x509.CertificateBuilder().subject_name(
                subject
            ).issuer_name(
                issuer
            ).public_key(
                ca_private_key.public_key()
            ).serial_number(
                x509.random_serial_number()
            ).not_valid_before(
                datetime.utcnow()
            ).not_valid_after(
                datetime.utcnow() + timedelta(days=3650)  # 10年有效期
            ).add_extension(
                x509.SubjectAlternativeName([
                    x509.DNSName("localhost"),
                ]),
                critical=False,
            ).sign(ca_private_key, hashes.SHA256())
            
            logger.info("证书颁发机构初始化成功")
            return ca_private_key, ca_cert
            
        except Exception as e:
            logger.error(f"初始化CA失败: {e}")
            raise
    
    def register_device(self, device_info: Dict) -> Tuple[bool, str]:
        """
        注册新设备
        实现论文中的设备身份管理机制
        """
        try:
            device_id = device_info.get('device_id')
            if not device_id:
                return False, "设备ID不能为空"
            
            if device_id in self.devices:
                return False, "设备已存在"
            
            # 验证设备信息完整性
            required_fields = ['device_type', 'manufacturer', 'model', 
                             'firmware_version', 'mac_address', 'ip_address']
            for field in required_fields:
                if field not in device_info:
                    return False, f"缺少必要字段: {field}"
            
            # 生成设备密钥对
            device_private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048
            )
            device_public_key = device_private_key.public_key()
            
            # 生成设备证书
            device_cert = self._generate_device_certificate(
                device_id, device_public_key
            )
            
            # 计算安全等级
            security_level = self._calculate_security_level(device_info)
            
            # 创建设备信息对象
            device = DeviceInfo(
                device_id=device_id,
                device_type=device_info['device_type'],
                manufacturer=device_info['manufacturer'],
                model=device_info['model'],
                firmware_version=device_info['firmware_version'],
                mac_address=device_info['mac_address'],
                ip_address=device_info['ip_address'],
                public_key=device_public_key.public_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PublicFormat.SubjectPublicKeyInfo
                ).decode('utf-8'),
                certificate=device_cert.public_bytes(
                    serialization.Encoding.PEM
                ).decode('utf-8'),
                registration_time=time.time(),
                last_heartbeat=time.time(),
                status='online',
                security_level=security_level
            )
            
            self.devices[device_id] = device
            self.save_device_registry()
            
            logger.info(f"设备注册成功: {device_id}, 安全等级: {security_level}")
            return True, "设备注册成功"
            
        except Exception as e:
            logger.error(f"设备注册失败: {e}")
            return False, f"注册失败: {str(e)}"
    
    def _generate_device_certificate(self, device_id: str, 
                                   public_key: rsa.RSAPublicKey) -> x509.Certificate:
        """生成设备证书"""
        ca_private_key, ca_cert = self.certificate_authority
        
        subject = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "CN"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "IoT Device"),
            x509.NameAttribute(NameOID.COMMON_NAME, device_id),
        ])
        
        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            ca_cert.subject
        ).public_key(
            public_key
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.utcnow()
        ).not_valid_after(
            datetime.utcnow() + timedelta(days=SecurityConfig.DEVICE_CONFIG['certificate_validity'] // 86400)
        ).add_extension(
            x509.SubjectAlternativeName([
                x509.DNSName(device_id),
            ]),
            critical=False,
        ).sign(ca_private_key, hashes.SHA256())
        
        return cert
    
    def _calculate_security_level(self, device_info: Dict) -> int:
        """
        计算设备安全等级
        基于论文中的安全评估标准
        """
        score = 0
        
        # 制造商信誉评分
        trusted_manufacturers = ['Huawei', 'Xiaomi', 'Samsung', 'Apple']
        if device_info['manufacturer'] in trusted_manufacturers:
            score += 2
        
        # 固件版本新旧程度
        try:
            version_parts = device_info['firmware_version'].split('.')
            if len(version_parts) >= 3:
                major_version = int(version_parts[0])
                if major_version >= 2:
                    score += 1
        except:
            pass
        
        # 设备类型安全性
        high_security_types = ['industrial_sensor', 'medical_device', 'security_camera']
        if device_info['device_type'] in high_security_types:
            score += 2
        
        # 转换为1-5等级
        if score >= 4:
            return 5
        elif score >= 3:
            return 4
        elif score >= 2:
            return 3
        elif score >= 1:
            return 2
        else:
            return 1
    
    def authenticate_device(self, device_id: str, signature: str, 
                          challenge: str) -> Tuple[bool, str]:
        """
        设备身份认证
        实现基于数字签名的认证机制
        """
        try:
            if device_id not in self.devices:
                return False, "设备未注册"
            
            device = self.devices[device_id]
            if device.status == 'blocked':
                return False, "设备已被阻止"
            
            # 验证数字签名
            public_key = serialization.load_pem_public_key(
                device.public_key.encode('utf-8')
            )
            
            try:
                public_key.verify(
                    signature.encode('utf-8'),
                    challenge.encode('utf-8'),
                    padding.PSS(
                        mgf=padding.MGF1(hashes.SHA256()),
                        salt_length=padding.PSS.MAX_LENGTH
                    ),
                    hashes.SHA256()
                )
                
                # 更新心跳时间
                device.last_heartbeat = time.time()
                device.status = 'online'
                
                logger.info(f"设备认证成功: {device_id}")
                return True, "认证成功"
                
            except Exception as verify_error:
                logger.warning(f"设备签名验证失败: {device_id}, {verify_error}")
                return False, "签名验证失败"
                
        except Exception as e:
            logger.error(f"设备认证异常: {e}")
            return False, f"认证异常: {str(e)}"
    
    def update_heartbeat(self, device_id: str) -> bool:
        """更新设备心跳"""
        if device_id in self.devices:
            self.devices[device_id].last_heartbeat = time.time()
            self.devices[device_id].status = 'online'
            return True
        return False
    
    def check_offline_devices(self) -> List[str]:
        """检查离线设备"""
        offline_devices = []
        current_time = time.time()
        threshold = SecurityConfig.DEVICE_CONFIG['offline_threshold']
        
        for device_id, device in self.devices.items():
            if current_time - device.last_heartbeat > threshold:
                device.status = 'offline'
                offline_devices.append(device_id)
        
        if offline_devices:
            logger.warning(f"发现离线设备: {offline_devices}")
        
        return offline_devices
    
    def block_device(self, device_id: str, reason: str) -> bool:
        """阻止设备"""
        if device_id in self.devices:
            self.devices[device_id].status = 'blocked'
            logger.warning(f"设备已被阻止: {device_id}, 原因: {reason}")
            return True
        return False
    
    def get_device_info(self, device_id: str) -> Optional[DeviceInfo]:
        """获取设备信息"""
        return self.devices.get(device_id)
    
    def get_all_devices(self) -> Dict[str, DeviceInfo]:
        """获取所有设备信息"""
        return self.devices.copy()
    
    def save_device_registry(self):
        """保存设备注册表"""
        try:
            registry_data = {}
            for device_id, device in self.devices.items():
                registry_data[device_id] = asdict(device)
            
            with open(self.device_registry_file, 'w', encoding='utf-8') as f:
                json.dump(registry_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"保存设备注册表失败: {e}")
    
    def load_device_registry(self):
        """加载设备注册表"""
        try:
            with open(self.device_registry_file, 'r', encoding='utf-8') as f:
                registry_data = json.load(f)
                
            for device_id, device_data in registry_data.items():
                self.devices[device_id] = DeviceInfo(**device_data)
                
            logger.info(f"加载设备注册表成功，共{len(self.devices)}个设备")
            
        except FileNotFoundError:
            logger.info("设备注册表文件不存在，创建新的注册表")
            self.devices = {}
        except Exception as e:
            logger.error(f"加载设备注册表失败: {e}")
            self.devices = {}
