"""
系统运行脚本
提供简单的命令行界面来运行和管理物联网安全防护系统
"""

import argparse
import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from main import IoTSecuritySystem
from core.device_manager import DeviceManager
from core.crypto_engine import CryptoEngine
from core.threat_detector import ThreatDetector
from utils.logger import get_logger

logger = get_logger(__name__)

def create_directories():
    """创建必要的目录"""
    directories = [
        'data',
        'data/security_logs',
        'certs',
        'keys',
        'ca'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"创建目录: {directory}")

def test_modules():
    """测试各个模块"""
    logger.info("开始模块测试...")
    
    try:
        # 测试设备管理器
        logger.info("测试设备管理器...")
        device_manager = DeviceManager()
        
        test_device = {
            'device_id': 'test_001',
            'device_type': 'sensor',
            'manufacturer': 'TestCorp',
            'model': 'T-100',
            'firmware_version': '1.0.0',
            'mac_address': '00:11:22:33:44:55',
            'ip_address': '***********00'
        }
        
        success, message = device_manager.register_device(test_device)
        if success:
            logger.info("✓ 设备管理器测试通过")
        else:
            logger.error(f"✗ 设备管理器测试失败: {message}")
        
        # 测试加密引擎
        logger.info("测试加密引擎...")
        crypto_engine = CryptoEngine()
        
        # 测试密钥生成
        sm4_key = crypto_engine.generate_key('SM4')
        led_key = crypto_engine.generate_key('LED')
        
        # 测试加密解密
        test_data = b"Hello, IoT Security!"
        
        # SM4加密测试
        encrypt_result = crypto_engine.encrypt_data(test_data, 'SM4', sm4_key)
        if encrypt_result['success']:
            decrypt_result = crypto_engine.decrypt_data(
                encrypt_result['ciphertext'], 'SM4', sm4_key, encrypt_result['iv']
            )
            if decrypt_result['success'] and decrypt_result['plaintext'] == test_data:
                logger.info("✓ SM4加密引擎测试通过")
            else:
                logger.error("✗ SM4解密测试失败")
        else:
            logger.error("✗ SM4加密测试失败")
        
        # LED加密测试
        encrypt_result = crypto_engine.encrypt_data(test_data, 'LED', led_key)
        if encrypt_result['success']:
            decrypt_result = crypto_engine.decrypt_data(
                encrypt_result['ciphertext'], 'LED', led_key
            )
            if decrypt_result['success'] and decrypt_result['plaintext'] == test_data:
                logger.info("✓ LED加密引擎测试通过")
            else:
                logger.error("✗ LED解密测试失败")
        else:
            logger.error("✗ LED加密测试失败")
        
        # 测试威胁检测器
        logger.info("测试威胁检测器...")
        threat_detector = ThreatDetector()
        
        # 添加一些测试流量
        from core.threat_detector import NetworkFlow
        import time
        
        test_flows = []
        for i in range(10):
            flow = NetworkFlow(
                src_ip=f"192.168.1.{100+i}",
                dst_ip="***********",
                src_port=12345,
                dst_port=80,
                protocol='TCP',
                packet_count=1,
                byte_count=64,
                duration=0.1,
                flags=['SYN'],
                timestamp=time.time()
            )
            test_flows.append(flow)
            threat_detector.add_network_flow(flow)
        
        threats = threat_detector.detect_threats()
        logger.info(f"✓ 威胁检测器测试通过，检测到{len(threats)}个威胁")
        
        logger.info("所有模块测试完成")
        return True
        
    except Exception as e:
        logger.error(f"模块测试失败: {e}")
        return False

def show_system_info():
    """显示系统信息"""
    from config.settings import SecurityConfig
    
    print("=" * 60)
    print("物联网安全防护系统信息")
    print("=" * 60)
    print(f"系统名称: {SecurityConfig.SYSTEM_NAME}")
    print(f"版本: {SecurityConfig.VERSION}")
    print(f"调试模式: {'开启' if SecurityConfig.DEBUG else '关闭'}")
    print(f"数据库路径: {SecurityConfig.DATABASE['path']}")
    print(f"日志目录: {SecurityConfig.LOGGING['log_dir']}")
    print(f"最大设备数: {SecurityConfig.DEVICE_CONFIG['max_devices']}")
    print(f"威胁检测间隔: {SecurityConfig.THREAT_DETECTION['detection_interval']}秒")
    print("=" * 60)
    
    # 显示支持的算法
    crypto_engine = CryptoEngine()
    algo_info = crypto_engine.get_algorithm_info()
    
    print("支持的加密算法:")
    print("对称加密:", ", ".join(algo_info['symmetric'].keys()))
    print("非对称加密:", ", ".join(algo_info['asymmetric'].keys()))
    print("哈希算法:", ", ".join(algo_info['hash'].keys()))
    print("=" * 60)

async def run_system():
    """运行系统"""
    system = IoTSecuritySystem()
    try:
        await system.start()
    except KeyboardInterrupt:
        logger.info("接收到中断信号，正在停止系统...")
    except Exception as e:
        logger.error(f"系统运行异常: {e}")
    finally:
        await system.stop()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='物联网安全防护系统')
    parser.add_argument('--action', choices=['run', 'test', 'info', 'setup'], 
                       default='run', help='执行的操作')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    
    args = parser.parse_args()
    
    if args.debug:
        from config.settings import SecurityConfig
        SecurityConfig.DEBUG = True
        SecurityConfig.LOGGING['level'] = 'DEBUG'
    
    if args.action == 'setup':
        logger.info("初始化系统环境...")
        create_directories()
        logger.info("系统环境初始化完成")
        
    elif args.action == 'test':
        logger.info("开始系统测试...")
        create_directories()
        if test_modules():
            logger.info("系统测试通过")
            sys.exit(0)
        else:
            logger.error("系统测试失败")
            sys.exit(1)
            
    elif args.action == 'info':
        show_system_info()
        
    elif args.action == 'run':
        logger.info("启动物联网安全防护系统...")
        create_directories()
        
        # 可选：运行测试
        if not args.debug:
            logger.info("运行快速测试...")
            if not test_modules():
                logger.warning("模块测试失败，但继续运行系统")
        
        # 运行系统
        try:
            asyncio.run(run_system())
        except KeyboardInterrupt:
            logger.info("程序被用户中断")
        except Exception as e:
            logger.error(f"程序异常退出: {e}")
            sys.exit(1)

if __name__ == "__main__":
    main()
